@font-face {
    font-family: 'Concrete';
    src: url('../fonts/Concrete-2.ttf') format('truetype');
    /* src: url('../fonts/MinecraftRegular-Bmg3.otf') format('truetype'); */
    font-weight: normal;
    font-style: normal;
}

/* 基础样式 */
body {
    background-attachment: fixed;
    background-repeat: no-repeat;
    position: relative;
    background-color: black;
    font-family: Concrete, Arial, Helvetica, sans-serif;
    overflow: hidden;
    height: 100vh;
    margin: 0;
    padding: 0;
    cursor: auto; /* 恢复默认光标 */
}

/* 粒子背景画布 */
#particle-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

/* 鼠标跟随光晕 */
#cursor-glow {
    position: fixed;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, rgba(232, 67, 147, 0.4) 0%, rgba(232, 67, 147, 0.2) 30%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transform: translate(-50%, -50%);
    transition: all 0.1s ease;
}

/* main 容器样式 */
.main {
    max-width: 600px;
    padding: 20px;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    height: auto;
    max-height: 85vh;
    overflow-y: auto;
    width: 90%;
    z-index: 10;
    transition: transform 0.3s ease;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.main::-webkit-scrollbar {
    display: none;
}

.main:hover {
    transform: translate(-50%, -50%) scale(1.02);
}

/* 标题样式 */
h1 {
    color: #e84393;
    font-size: 3.5rem;
    position: relative;
    margin: 0;
    text-align: center;
}

/* 打字机光标效果 */
#typewriter-title::after {
    content: '|';
    color: #e84393;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

h1::before {
    content: 'METAQIU\'S HOME';
    position: absolute;
    color: #b2bec348;
    z-index: -1;
    left: 10px;
    top: 10px;
}

/* 内容样式 */
.content {
    padding: 20px 0;
    flex: 0 0 auto;
}

p {
    color: #b2bec3;
    font-size: 1.6rem;
    margin: 0 0 10px 0;
    line-height: 1.8;
}

/* 社交链接样式 */
.social-links {
    text-align: center;
    margin: 0px 0 20px 0;
}

.social-item {
    display: inline-block;
    margin: 0 20px;
    font-size: 2rem;
    color: #b2bec3;
}

/* 简单的颜色过渡效果 */
.social-item:hover,
.content p:hover {
    color: #e84393;
}

/* 服务栏样式 */
.services-bar {
    margin: 20px 0;
    text-align: left;
    padding: 10px 0;
    border-top: 1px solid rgba(178, 190, 195, 0.2);
}

.services-title {
    color: #b2bec3;
    font-size: 1.1rem;
    margin-bottom: 10px;
    opacity: 0.7;
}

.services-items {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 15px;
}

.service-item {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: #b2bec3;
    text-decoration: none;
    font-size: 1.1rem;
    transition: color 0.3s ease;
    opacity: 0.8;
}

.service-item:hover {
    color: #e84393;
    opacity: 1;
}

.service-item i {
    font-size: 1.1rem;
}

/* 背景区域样式 */
.background-area {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.6), rgba(232, 67, 147, 0.15));
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* 添加额外的覆盖层确保文字可读性 */
.background-area::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 30%, rgba(0, 0, 0, 0.3) 70%);
    z-index: 1;
}

.background-area img {
    filter: blur(8px) brightness(0.85) contrast(1.1) saturate(1.2);
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    animation: slowFloat 20s ease-in-out infinite;
    transition: filter 0.3s ease;
}

/* 统一背景图片样式 */
.background-area .universal-bg-img {
    display: block;
    object-fit: contain;
    width: 100%;
    height: 100%;
}

/* 针对横屏设备使用 cover 模式 */
@media screen and (min-aspect-ratio: 4/3) {
    .background-area .universal-bg-img {
        object-fit: cover;
    }
}

/* 针对竖屏设备使用 contain 模式确保完整显示 */
@media screen and (max-aspect-ratio: 4/3) {
    .background-area .universal-bg-img {
        object-fit: contain;
    }
}

/* 为所有设备添加深色遮罩以提高文字可读性 */
.background-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
    pointer-events: none;
}

/* 鼠标悬停时稍微增强背景清晰度 */
.main:hover ~ .background-area img {
    filter: blur(6px) brightness(0.9) contrast(1.15) saturate(1.3);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes slowFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-10px) scale(1.02); }
}

/* 页脚样式 */
.footer {
    margin-top: auto;
    padding: 15px 0;
    border-top: 1px solid rgba(178, 190, 195, 0.2);
    color: #999;
    font-family: Arial;
    font-size: 0.9rem;
    line-height: 1.5;
}

.footer a {
    color: #999;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    #cursor-glow {
        display: none; /* 移动端隐藏光晕 */
    }

    .background-area img {
        filter: blur(6px) brightness(0.8) contrast(1.05) saturate(1.1);
        /* 移动端图片适配 - 确保完整显示 */
        object-fit: contain;
        width: 100%;
        height: 100%;
    }

    .background-area {
        background: linear-gradient(45deg, rgba(0, 0, 0, 0.7), rgba(232, 67, 147, 0.12));
    }

    .main {
        width: 95%;
        padding: 15px;
        max-height: 90vh;
        transform: translate(-50%, -50%);
    }

    .main:hover {
        transform: translate(-50%, -50%) scale(1.01);
    }

    h1 {
        font-size: 2.8rem;
    }

    p {
        font-size: 1.2rem;
    }

    .social-item:hover {
        transform: translateY(-3px) scale(1.05);
    }

    .services-bar {
        margin: 15px 0;
    }

    .services-title {
        font-size: 1.0rem;
    }

    .service-item {
        font-size: 1.0rem;
    }
}

@media screen and (max-width: 480px) {
    .background-area img {
        filter: blur(5px) brightness(0.75) contrast(1.0) saturate(1.0);
        /* 小屏幕图片适配 - 确保完整显示 */
        object-fit: cover;
        width: 100%;
        height: 100%;
    }

    .background-area {
        background: linear-gradient(45deg, rgba(0, 0, 0, 0.75), rgba(232, 67, 147, 0.1));
    }

    .main {
        width: 95%;
        padding: 10px;
    }

    h1 {
        font-size: 2.2rem;
    }

    p {
        font-size: 1.0rem;
    }

    .services-bar {
        margin: 12px 0;
    }

    .services-title {
        font-size: 0.95rem;
    }

    .service-item {
        font-size: 0.95rem;
    }
}

/* 高分辨率屏幕优化 */
@media screen and (min-width: 1200px) {
    .background-area img {
        filter: blur(10px) brightness(0.9) contrast(1.15) saturate(1.3);
    }

    .background-area {
        background: linear-gradient(45deg, rgba(0, 0, 0, 0.5), rgba(232, 67, 147, 0.18));
    }
}

/* 超高分辨率屏幕优化 */
@media screen and (min-width: 1600px) {
    .background-area img {
        filter: blur(12px) brightness(0.95) contrast(1.2) saturate(1.4);
    }
}

/* 自我介绍部分样式 */
.intro-section {
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid rgba(178, 190, 195, 0.2);
}

.intro-text {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #e84393;
}

.intro-detail {
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 8px 0;
    opacity: 0.9;
}

/* 标题背景文字效果 */
h1::before {
    content: 'METAQIU\'S HOME';
    position: absolute;
    color: #b2bec348;
    z-index: -1;
    left: 10px;
    top: 10px;
}

/* 彩虹文字动画 */
@keyframes rainbowText {
    0% { color: #ff2a2a; text-shadow: 0 0 10px rgba(255, 42, 42, 0.7); transform: scale(1); }
    15% { color: #ff7a2a; text-shadow: 0 0 12px rgba(255, 122, 42, 0.7); transform: scale(1.02); }
    30% { color: #ffc52a; text-shadow: 0 0 15px rgba(255, 197, 42, 0.7); transform: scale(1.04); }
    45% { color: #43d12c; text-shadow: 0 0 15px rgba(67, 209, 44, 0.7); transform: scale(1.05); }
    60% { color: #2ac3d1; text-shadow: 0 0 15px rgba(42, 195, 209, 0.7); transform: scale(1.04); }
    75% { color: #6b2ad1; text-shadow: 0 0 12px rgba(107, 42, 209, 0.7); transform: scale(1.02); }
    90% { color: #d12a9c; text-shadow: 0 0 10px rgba(209, 42, 156, 0.7); transform: scale(1); }
    100% { color: #ff2a2a; text-shadow: 0 0 10px rgba(255, 42, 42, 0.7); transform: scale(1); }
}

/* 打字机标题样式 */
#typewriter-title {
    animation: rainbowText 8s linear infinite;
    position: relative;
    letter-spacing: 2px;
    font-weight: 800;
    -webkit-text-stroke: 0.5px rgba(255, 255, 255, 0.3);
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

/* 社交链接样式 */
.social-links .social-item {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-links .social-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.social-links .social-item:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    animation: pulse 1s infinite;
}

/* 脉冲动画 */
@keyframes pulse {
    0% { opacity: 0.8; transform: scale(0.8); }
    100% { opacity: 0; transform: scale(1.5); }
}

/* 介绍部分样式 */
.intro-section {
    transition: all 0.5s ease;
}

.intro-section:hover .intro-text {
    animation: glow 1.5s ease-in-out infinite alternate;
}

/* 发光效果动画 */
@keyframes glow {
    from {
        text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #e60073, 0 0 20px #e60073;
    }
    to {
        text-shadow: 0 0 10px #fff, 0 0 20px #ff4da6, 0 0 30px #ff4da6, 0 0 40px #ff4da6;
    }
}

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 标题动画 */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 内容动画 */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 社交图标动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 应用动画到元素 */
.title.animate {
    animation: slideInFromTop 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.content p.animate {
    opacity: 0;
    animation: slideInFromRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.content p.animate:nth-child(2) {
    animation-delay: 0.2s;
}

.content p.animate:nth-child(3) {
    animation-delay: 0.4s;
}

.intro-section.animate {
    opacity: 0;
    animation: fadeIn 0.8s ease forwards;
    animation-delay: 0.6s;
}

.social-links.animate {
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.8s;
}

.services-bar.animate {
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 1.0s;
}

/* 优化悬停效果 */
.social-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-item:hover {
    transform: translateY(-5px) scale(1.1) rotate(5deg);
    color: #e84393;
    filter: drop-shadow(0 8px 15px rgba(232, 67, 147, 0.4));
    text-shadow: 0 0 20px rgba(232, 67, 147, 0.6);
}

/* 标题悬停效果 */
h1 {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

h1:hover {
    text-shadow: 
        0 0 10px rgba(232, 67, 147, 0.3),
        0 0 20px rgba(232, 67, 147, 0.2);
    transform: scale(1.02);
}

/* 修改自我介绍悬停效果 */
.intro-detail {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding-left: 5px;
}

/* 为第2、3、4个intro-detail添加删除线 */
.intro-detail:nth-child(n+2):nth-child(-n+4) {
    text-decoration: line-through;
    text-decoration-color: rgba(232, 67, 147, 0.5);  /* 使用半透明的粉色 */
    text-decoration-thickness: 2px;  /* 设置线的粗细 */
}

.intro-detail:hover {
    color: #e84393;
    padding-left: 10px;
}

.intro-detail:hover::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 3px;
    height: 70%;
    background: #e84393;
    transform: translateY(-50%);
    border-radius: 2px;
}

/* 运行时间样式 */
#runtime {
    font-size: 0.9rem;
    color: #b2bec3;
    margin-top: 10px;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    background: rgba(178, 190, 195, 0.1);
}

.visitor {
    font-size: 0.9rem;
    color: #b2bec3;
    margin-top: 10px;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    background: rgba(178, 190, 195, 0.1);
}

/* 访客统计数字加粗 */
.visitor #page_pv, .visitor #page_uv {
    font-weight: bold;
}