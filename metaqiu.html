<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="keywords" content="MetaQiu, MetaQiu的球球空间" />
    <meta name="description" content="MetaQiu的球球空间，分享记录日常生活中的所遇。" />
    <meta name="viewport" content="width=device-width, initial-scale=0.8, user-scalable=1, minimum-scale=0.5, maximum-scale=2.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <!-- favicon -->
    <link rel="icon" href="./favicon.ico" type="image/x-icon">
    <!-- title -->
    <title>千小秋的自留地</title>
    <!-- css -->
    <link href="./css/style.css" rel="stylesheet" type="text/css" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <!-- 粒子背景画布 -->
    <canvas id="particle-canvas"></canvas>

    <!-- 鼠标跟随光标 -->
    <div id="cursor-glow"></div>

    <div class="background-area">
        <!-- 大屏幕使用metaqiu.png -->
        <img class="lazy-img desktop-img" data-original="./images/metaqiu.png" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAEALAAAAAABAAEAAAICRAEAOw==">
        <!-- 移动端和小屏幕使用原图片 -->
        <img class="lazy-img mobile-img" data-original="./images/wallhaven-8xd6p1.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAEALAAAAAABAAEAAAICRAEAOw==">
    </div>

    <div class="main">
        
        <div class="title animate">
            <h1 id="typewriter-title"></h1>
        </div>
        <br>
        <br>
        <div class="content">
            <p class="animate" style="font-size: 1.2rem; line-height: 10px;">人生就是一个故事。</p>
            <br>
            <p class="animate" style="font-size: 1.2rem; line-height: 10px;">所以，请享受生命，热爱生活吧！</p>
            <br>
            <p class="animate" style="font-size: 1.2rem; line-height: 10px;">" 艺术家创造美，而不是接受现成的美 " &nbsp— &nbsp歌德</p>
            
            <div class="intro-section animate">
                <p class="intro-text">👋 你好，我是 MetaQiu</p>
                <p class="intro-detail">🎨 热爱设计与编程的创作者</p>
                <p class="intro-detail">💡 正在探索有趣的数字世界</p>
                <p class="intro-detail">🌱 永远保持学习的热情</p>
                <p class="intro-detail">🌈 Vibe Coding 大师</p>
            </div>
        </div>

        <div class="social-links animate">
            <a href="https://github.com/metaqiu" target="_blank" class="social-item">
                <i class="fab fa-github"></i>
            </a>
            <a href="mailto:<EMAIL>" class="social-item">
                <i class="fas fa-envelope"></i>
            </a>
            <a href="https://twitter.com/" target="_blank" class="social-item">
                <i class="fab fa-twitter"></i>
            </a>
        </div>

        <!-- 服务栏 -->
        <div class="services-bar animate">
            <div class="services-title">服务</div>
            <div class="services-items">
                <a href="https://mail.metaqiu.cn/login" target="_blank" class="service-item">
                    <i class="fas fa-envelope"></i>
                    <span>临时邮箱</span>
                </a>

                <a href="https://docker.metaqiu.cn" target="_blank" class="service-item">
                    <i class="fab fa-docker"></i>
                    <span>加速镜像</span>
                </a>
                <a href="https://api.metaqiu.cn" target="_blank" class="service-item">
                    <i class="fas fa-robot"></i>
                    <span>LLM接口</span>
                </a>
                <a href="https://status.metaqiu.cn" target="_blank" class="service-item">
                    <i class="fas fa-cloud"></i>
                    <span>状态监控</span>
                </a>
<!--                <a href="#" class="service-item">
-->
<!--                    <i class="fas fa-blog"></i>
-->
<!--                    <span>博客</span>
-->
<!--                </a>
-->
                <a href="https://visitor.metaqiu.cn" target="_blank" class="service-item">
                    <i class="fas fa-tools"></i>
                    <span>统计工具</span>
                </a>
            </div>
        </div>

        <div class="footer">
            <!-- New Footer -->
            <a class="Copyright"><span>Copyright © 2024 - <script>document.write(new Date().getFullYear());</script> MetaQiu All Rights Reserved.</span></a>
                <br>
            <a class="test-decoration">In the next two years, unlimited imagination and miracles.</a>
                <br>
            本网站上的所有内容（包括但不限于文章、图像）
            <br>
            根据<a href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="license">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
            <br>
            <span id="runtime">本站已运行: 0天0小时0分0秒</span>
            <span class="visitor">
                访问量: <span id="page_pv">--</span> | 访客数: <span id="page_uv">--</span>
            </span>
        </div>

    </div>

    <script src="./js/jquery.min.js"></script>
    <script src="./js/jquery.lazyload.min.js"></script>
    <script>
        // 粒子系统
        class ParticleSystem {
            constructor() {
                this.canvas = document.getElementById('particle-canvas');
                this.ctx = this.canvas.getContext('2d');
                this.particles = [];
                this.mouse = { x: 0, y: 0 };

                this.resize();
                this.init();
                this.animate();

                window.addEventListener('resize', () => this.resize());
                document.addEventListener('mousemove', (e) => {
                    this.mouse.x = e.clientX;
                    this.mouse.y = e.clientY;
                });
            }

            resize() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
            }

            init() {
                for (let i = 0; i < 100; i++) {
                    this.particles.push({
                        x: Math.random() * this.canvas.width,
                        y: Math.random() * this.canvas.height,
                        vx: (Math.random() - 0.5) * 0.5,
                        vy: (Math.random() - 0.5) * 0.5,
                        size: Math.random() * 2 + 1,
                        opacity: Math.random() * 0.5 + 0.2
                    });
                }
            }

            animate() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                this.particles.forEach(particle => {
                    // 更新位置
                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    // 边界检测
                    if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;

                    // 鼠标交互
                    const dx = this.mouse.x - particle.x;
                    const dy = this.mouse.y - particle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 100) {
                        particle.x -= dx * 0.01;
                        particle.y -= dy * 0.01;
                    }

                    // 绘制粒子
                    this.ctx.beginPath();
                    this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    this.ctx.fillStyle = `rgba(232, 67, 147, ${particle.opacity})`;
                    this.ctx.fill();
                });

                requestAnimationFrame(() => this.animate());
            }
        }

        // 循环打字机效果
        function typeWriterLoop(element, text, typeSpeed = 150, deleteSpeed = 100, pauseTime = 2000) {
            let i = 0;
            let isDeleting = false;

            function type() {
                if (!isDeleting) {
                    // 打字阶段
                    if (i < text.length) {
                        element.innerHTML = text.substring(0, i + 1);
                        i++;
                        setTimeout(type, typeSpeed);
                    } else {
                        // 打字完成，等待后开始删除
                        setTimeout(() => {
                            isDeleting = true;
                            type();
                        }, pauseTime);
                    }
                } else {
                    // 删除阶段
                    if (i > 0) {
                        element.innerHTML = text.substring(0, i - 1);
                        i--;
                        setTimeout(type, deleteSpeed);
                    } else {
                        // 删除完成，重新开始打字
                        isDeleting = false;
                        setTimeout(type, 500);
                    }
                }
            }

            type();
        }

        // 鼠标跟随光晕
        function initCursorGlow() {
            const cursorGlow = document.getElementById('cursor-glow');

            document.addEventListener('mousemove', (e) => {
                cursorGlow.style.left = e.clientX + 'px';
                cursorGlow.style.top = e.clientY + 'px';
            });
        }

        // 智能懒加载 - 根据屏幕尺寸加载对应图片
        function initResponsiveLazyLoad() {
            const isDesktop = window.innerWidth >= 1024;
            const desktopImg = $('.desktop-img');
            const mobileImg = $('.mobile-img');

            if (isDesktop) {
                // 桌面端：显示桌面图片，隐藏移动图片
                desktopImg.show().addClass('lazyload').lazyload({
                    effect: 'fadeIn',
                    effectspeed: 1000
                });
                mobileImg.hide().removeClass('lazyload');
            } else {
                // 移动端：显示移动图片，隐藏桌面图片
                mobileImg.show().addClass('lazyload').lazyload({
                    effect: 'fadeIn',
                    effectspeed: 1000
                });
                desktopImg.hide().removeClass('lazyload');
            }
        }

        // 窗口大小改变时重新初始化
        let resizeTimer;
        function handleResize() {
            // 防抖处理，避免频繁触发
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                // 清除之前的懒加载
                $('.background-area img').removeClass('lazyload');

                // 重新初始化懒加载
                initResponsiveLazyLoad();
            }, 250);
        }

        window.onload = function() {
            // 初始化响应式懒加载
            initResponsiveLazyLoad();

            // 初始化特效
            new ParticleSystem();
            initCursorGlow();

            // 循环打字机效果
            setTimeout(() => {
                const titleElement = document.getElementById('typewriter-title');
                typeWriterLoop(titleElement, "METAQIU'S HOME", 150, 100, 2500);
            }, 500);
        }

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);

        // 添加运行时间统计
        function showRuntime() {
            const startTime = new Date("2024/01/01 00:00:00");
            const currentTime = new Date();
            const runTime = currentTime - startTime;

            const day = Math.floor(runTime / (24 * 60 * 60 * 1000));
            const hour = Math.floor((runTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
            const minute = Math.floor((runTime % (60 * 60 * 1000)) / (60 * 1000));
            const second = Math.floor((runTime % (60 * 1000)) / 1000);

            document.getElementById("runtime").innerHTML =
                `本站已运行: ${day}天${hour}小时${minute}分${second}秒`;
        }

        // 每秒更新一次
        setInterval(showRuntime, 1000);
        showRuntime();
    </script>

    <!-- AnalyEdge 访客统计 -->
    <script defer src="https://visitor.metaqiu.cn/js/index.min.js"></script>
</body>
</html>
